# Design Prompt: Modern Design Studio Website (Rerter-Inspired)

## Project Brief
Create a sophisticated, award-worthy website for a design studio that showcases exceptional digital craftsmanship through immersive animations, clean aesthetics, and intuitive user experience. The site should demonstrate the studio's expertise while providing an engaging journey for potential clients.

## Visual Design System

### Color Palette
- **Primary:** Pure black (#000000) and white (#FFFFFF)
- **Accent:** Single vibrant color for highlights (user's choice)
- **Grays:** Subtle gray tones for depth and hierarchy
- **Background:** Clean white with occasional dark sections for contrast

### Typography
- **Headlines:** Bold, modern sans-serif (e.g., Inter, Helvetica Neue, or custom)
- **Body Text:** Clean, readable sans-serif with excellent legibility
- **Sizes:** Large, impactful headlines (48px-120px), comfortable body text (16px-18px)
- **Weight:** Mix of light, regular, and bold weights for hierarchy

### Layout Principles
- **Minimalism:** Generous whitespace and clean compositions
- **Grid System:** Consistent spacing and alignment
- **Responsive:** Mobile-first approach with fluid layouts
- **Hierarchy:** Clear visual hierarchy through size, weight, and spacing

## Page Structure & Animations

### 1. Hero Section
**Design:**
- Full-screen viewport height
- Centered, large headline text
- Rotating taglines or service descriptions
- Subtle animated background elements
- Minimal navigation overlay

**Animations:**
```
- Initial page load: Fade-in sequence (0.8s ease-out)
- Headline: Slide-up animation with slight delay (1s power2.out)
- Rotating text: Cross-fade transitions every 3-4 seconds
- Background: Subtle floating particles or geometric shapes
- Mouse tracking: Gentle parallax movement (±10px)
```

### 2. Navigation System
**Structure:**
- Sticky header with transparent background
- Studio logo/name (left)
- Hamburger menu icon (right)
- "Contact" or "Let's Talk" CTA (right)

**Menu Overlay:**
- Full-screen dark overlay (90% opacity)
- Large navigation links with staggered animations
- Social media icons
- Contact information

**Interactions:**
```
- Menu open: Scale and fade-in overlay (0.6s ease-out)
- Menu items: Staggered slide-up (0.1s delays)
- Hover states: Underline animation (0.3s ease-in-out)
- Close: Reverse animation sequence
```

### 3. "Our Work" Portfolio Section
**Layout:**
- Horizontal carousel/slider
- Full-width project panels
- Large hero images with overlay text
- Project navigation (arrows/dots)

**Content Structure:**
```
Each Project Panel:
- Hero image (70% of viewport)
- Project title (large, bold)
- Project category/type
- Brief description
- "View Case Study" CTA
```

**Animations:**
```
- Panel transitions: Smooth slide (0.8s power2.inOut)
- Image hover: Subtle scale (1.05x, 0.4s ease-out)
- Text reveals: Fade-up with stagger (0.5s ease-out)
- Background: Color shift between projects
- Cursor: Custom cursor over interactive areas
```

### 4. Services/Capabilities Section
**Design:**
- Grid layout of service cards
- Icon or illustration for each service
- Clear, concise descriptions
- Hover interactions

**Animations:**
```
- Scroll trigger: Cards fade-in with stagger (0.6s ease-out)
- Hover: Card lift effect (translateY: -10px, 0.3s ease-out)
- Icons: Subtle rotation or scale on hover
- Text: Color transition on hover
```

### 5. About/Studio Section
**Content:**
- Studio philosophy
- Team highlights
- Awards/recognition
- Process overview

**Animations:**
```
- Text blocks: Fade-in on scroll (0.7s ease-out)
- Images: Parallax movement (0.5x scroll speed)
- Statistics: Count-up animations
- Team photos: Hover reveal effects
```

### 6. Contact Section
**Design:**
- Clean contact form
- Studio location/info
- Social media links
- Call-to-action

**Interactions:**
```
- Form fields: Focus animations (border color, 0.3s ease)
- Submit button: Loading state animation
- Success: Confirmation animation
- Map: Interactive hover states
```

## Technical Implementation

### Animation Library: GSAP
```javascript
// Example scroll trigger setup
gsap.registerPlugin(ScrollTrigger);

// Hero text animation
gsap.timeline()
  .from(".hero-title", {
    y: 100,
    opacity: 0,
    duration: 1,
    ease: "power2.out"
  })
  .from(".hero-subtitle", {
    y: 50,
    opacity: 0,
    duration: 0.8,
    ease: "power2.out"
  }, "-=0.5");

// Portfolio carousel
gsap.to(".portfolio-panel", {
  x: "-100%",
  duration: 0.8,
  ease: "power2.inOut"
});
```

### Scroll Triggers
```javascript
// Section reveals
gsap.utils.toArray(".section").forEach(section => {
  gsap.from(section, {
    y: 100,
    opacity: 0,
    duration: 1,
    ease: "power2.out",
    scrollTrigger: {
      trigger: section,
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });
});
```

### Performance Considerations
- Lazy loading for images
- Optimized animation performance (transform/opacity only)
- Reduced motion for accessibility
- Mobile-specific optimizations

## Interactive Elements

### Cursor Effects
- Custom cursor design
- Hover state transformations
- Magnetic effect on buttons
- Text selection styling

### Micro-Interactions
- Button hover animations
- Link underline effects
- Form field focus states
- Loading indicators

### Scroll Enhancements
- Smooth scrolling
- Progress indicators
- Parallax backgrounds
- Sticky elements

## Responsive Design

### Breakpoints
- Mobile: 320px-768px
- Tablet: 768px-1024px
- Desktop: 1024px+

### Mobile Adaptations
- Simplified animations
- Touch-friendly interactions
- Swipe gestures for carousel
- Optimized performance

## Content Strategy

### Messaging
- Clear value proposition
- Compelling case studies
- Process transparency
- Client testimonials

### Visual Content
- High-quality project imagery
- Behind-the-scenes content
- Team photography
- Process illustrations

## Success Metrics

### User Experience
- Low bounce rate
- High time on site
- Portfolio engagement
- Contact form conversions

### Technical Performance
- Fast loading times (<3s)
- Smooth animations (60fps)
- Mobile optimization
- Accessibility compliance

---

**Implementation Notes:**
- Use modern build tools (Vite, Webpack)
- Implement proper SEO practices
- Ensure accessibility standards (WCAG 2.1)
- Test across devices and browsers
- Optimize for Core Web Vitals
